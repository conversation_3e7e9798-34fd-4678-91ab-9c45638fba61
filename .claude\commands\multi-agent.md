# Multi-Agent Analysis

```bash
# Multiple perspectives on the same codebase
/multi-agent-review comprehensive architecture and code review
/security-scan audit security posture  
/performance-optimization identify and fix bottlenecks
# Then consolidate findings
# Multi-perspective code review
/multi-agent-review Review authentication module
# Full-stack optimization
/multi-agent-optimize Optimize checkout flow for better conversion
# Review microservices architecture
/multi-agent-review analyze our microservices for coupling and scalability issues
# Comprehensive code review
/full-review Review the authentication module
```

Run these 5 agents in parallel using --ultrathink --seq --uc

- *architect-reviewer* - Reviews code changes through an architectural lens, ensuring consistency with established patterns and principles
- *backend-architect* - Reviews system architecture for scalability and performance bottlenecks
- *code-reviewer* - Expert code review specialist. Reviews code quality, security, and performance.
- *docs-architect* - Creates comprehensive technical documentation from existing codebases. Analyzes architecture, design patterns, and implementation details to produce long-form technical manuals.
- *document-manager* - Expert documentation specialist. Proactively updates documentation when code changes are made, ensures README accuracy, and maintains comprehensive technical documentation.

# Flutter ProGuard Rules for Journeyman Jobs

# Keep Flutter engine classes
-keep class io.flutter.** { *; }
-keep class androidx.** { *; }

# Keep Firebase classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Keep application-specific classes
-keep class com.mccarty.journeymanjobs.** { *; }

# Security: Obfuscate sensitive classes while keeping API structure
-keep public class com.mccarty.journeymanjobs.MainActivity {
    public <methods>;
}

# Weather and location services
-keep class ** implements android.location.LocationListener { *; }
-keep class ** extends android.app.Service { *; }

# Preserve annotations for reflection
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions

# Electrical domain models (keep for Firestore serialization)
-keep class **.models.** { *; }
-keep class **.domain.** { *; }

# Remove debug logging in release
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
}